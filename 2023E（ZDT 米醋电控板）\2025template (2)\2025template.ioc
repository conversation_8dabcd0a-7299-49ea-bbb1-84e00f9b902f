#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART2_RX
Dma.Request1=USART6_RX
Dma.Request2=UART4_RX
Dma.RequestsNb=3
Dma.UART4_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.2.Instance=DMA1_Stream2
Dma.UART4_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.2.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.2.Mode=DMA_NORMAL
Dma.UART4_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.2.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.0.Instance=DMA1_Stream5
Dma.USART2_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.0.Mode=DMA_NORMAL
Dma.USART2_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART6_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_RX.1.Instance=DMA2_Stream1
Dma.USART6_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART6_RX.1.Mode=DMA_NORMAL
Dma.USART6_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART6_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP10=UART5
Mcu.IP11=USART1
Mcu.IP12=USART2
Mcu.IP13=USART3
Mcu.IP14=USART6
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM3
Mcu.IP8=TIM4
Mcu.IP9=UART4
Mcu.IPNb=15
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PE11
Mcu.Pin11=PE12
Mcu.Pin12=PE13
Mcu.Pin13=PE14
Mcu.Pin14=PB10
Mcu.Pin15=PB11
Mcu.Pin16=PD8
Mcu.Pin17=PD9
Mcu.Pin18=PD10
Mcu.Pin19=PD11
Mcu.Pin2=PH0-OSC_IN
Mcu.Pin20=PD12
Mcu.Pin21=PD13
Mcu.Pin22=PC6
Mcu.Pin23=PC7
Mcu.Pin24=PC8
Mcu.Pin25=PC9
Mcu.Pin26=PA9
Mcu.Pin27=PA10
Mcu.Pin28=PA11
Mcu.Pin29=PA12
Mcu.Pin3=PH1-OSC_OUT
Mcu.Pin30=PA13
Mcu.Pin31=PA14
Mcu.Pin32=PC12
Mcu.Pin33=PD2
Mcu.Pin34=PB4
Mcu.Pin35=PB5
Mcu.Pin36=PB6
Mcu.Pin37=PB7
Mcu.Pin38=PE0
Mcu.Pin39=PE1
Mcu.Pin4=PA0-WKUP
Mcu.Pin40=VP_SYS_VS_Systick
Mcu.Pin41=VP_TIM1_VS_ClockSourceINT
Mcu.Pin5=PA1
Mcu.Pin6=PA2
Mcu.Pin7=PA3
Mcu.Pin8=PE9
Mcu.Pin9=PE10
Mcu.PinsNb=42
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.11.0
MxDb.Version=DB.6.0.140
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.UART4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Mode=Asynchronous
PA0-WKUP.Signal=UART4_TX
PA1.Mode=Asynchronous
PA1.Signal=UART4_RX
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PA11.GPIO_Label=LED1
PA11.GPIO_PuPd=GPIO_PULLUP
PA11.Locked=true
PA11.PinState=GPIO_PIN_SET
PA11.Signal=GPIO_Output
PA12.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PA12.GPIO_Label=LED2
PA12.GPIO_PuPd=GPIO_PULLUP
PA12.Locked=true
PA12.PinState=GPIO_PIN_SET
PA12.Signal=GPIO_Output
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.Locked=true
PB5.Signal=S_TIM3_CH2
PB6.Locked=true
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Locked=true
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC12.Locked=true
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC6.Locked=true
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Locked=true
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PC8.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PC8.GPIO_Label=LED3
PC8.GPIO_PuPd=GPIO_PULLUP
PC8.Locked=true
PC8.PinState=GPIO_PIN_SET
PC8.Signal=GPIO_Output
PC9.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PC9.GPIO_Label=LED4
PC9.GPIO_PuPd=GPIO_PULLUP
PC9.Locked=true
PC9.PinState=GPIO_PIN_SET
PC9.Signal=GPIO_Output
PD10.GPIOParameters=GPIO_Speed,PinState
PD10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD10.Locked=true
PD10.PinState=GPIO_PIN_SET
PD10.Signal=GPIO_Output
PD11.GPIOParameters=GPIO_Speed,PinState
PD11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD11.Locked=true
PD11.PinState=GPIO_PIN_SET
PD11.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=S_TIM4_CH1
PD13.Locked=true
PD13.Signal=S_TIM4_CH2
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD8.Locked=true
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Locked=true
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE0.GPIOParameters=GPIO_PuPd,GPIO_Label
PE0.GPIO_Label=KEY1
PE0.GPIO_PuPd=GPIO_PULLUP
PE0.Locked=true
PE0.Signal=GPIO_Input
PE1.GPIOParameters=GPIO_PuPd,GPIO_Label
PE1.GPIO_Label=KEY2
PE1.GPIO_PuPd=GPIO_PULLUP
PE1.Locked=true
PE1.Signal=GPIO_Input
PE10.Locked=true
PE10.Signal=GPIO_Output
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE12.Locked=true
PE12.Signal=GPIO_Output
PE13.Locked=true
PE13.Signal=GPIO_Output
PE14.Locked=true
PE14.Signal=S_TIM1_CH4
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label
PE2.GPIO_Label=KEY3
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label
PE3.GPIO_Label=KEY4
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE9.Locked=true
PE9.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=2025template.ioc
ProjectManager.ProjectName=2025template
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true,8-MX_TIM4_Init-TIM4-false-HAL-true,9-MX_UART5_Init-UART5-false-HAL-true,10-MX_USART1_UART_Init-USART1-false-HAL-true,11-MX_USART2_UART_Init-USART2-false-HAL-true,12-MX_USART3_UART_Init-USART3-false-HAL-true,13-MX_USART6_UART_Init-USART6-false-HAL-true,14-MX_UART4_Init-UART4-false-HAL-true
RCC.48MHZClocksFreq_Value=40000000
RCC.AHBFreq_Value=80000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=40000000
RCC.APB1TimFreq_Value=80000000
RCC.APB2Freq_Value=80000000
RCC.APB2TimFreq_Value=80000000
RCC.CortexFreq_Value=80000000
RCC.EthernetFreq_Value=80000000
RCC.FCLKCortexFreq_Value=80000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=80000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=80000000
RCC.PLLCLKFreq_Value=80000000
RCC.PLLM=8
RCC.PLLN=80
RCC.PLLQCLKFreq_Value=40000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=80000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=160000000
RCC.VcooutputI2S=192000000
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation2 CH2,Channel-PWM Generation4 CH4,Prescaler,Period
TIM1.Period=100-1
TIM1.Prescaler=40-1
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=EncoderMode
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
board=custom
